import { createSlice } from '@reduxjs/toolkit';
import {
  fetchDashboardStats,
  fetchRecentActivity,
  fetchPendingApprovals,
  fetchAnalytics,
  fetchTopPerformers,
  fetchSystemHealth,
  fetchUsers,
  fetchUserById,
  createUser,
  updateUser,
  deleteUser,
  bulkUpdateUsers,
  bulkDeleteUsers,
  toggleUserStatus,
  verifyUser,
  fetchUserActivityHistory,
  fetchUserStats,
  fetchContent,
  fetchContentById,
  updateContent,
  approveContent,
  rejectContent,
  deleteContent,
  bulkApproveContent,
  bulkRejectContent,
  bulkDeleteContent,
  updateContentStatus,
  featureContent,
  unfeatureContent,
  fetchContentReviews,
  fetchContentStats,
  fetchBids,
  fetchBidById,
  approveBid,
  rejectBid,
  deleteBid,
  updateBid,
  bulkApproveBids,
  bulkRejectBids,
  bulkDeleteBids,
  getBidStats,
  fetchOffers,
  fetchOfferById,
  approveOffer,
  rejectOffer,
  deleteOffer,
  bulkApproveOffers,
  bulkRejectOffers,
  bulkDeleteOffers,
  getOfferStats,
  fetchCMSPages,
  deleteCMSPage,
  updateCMSPageStatusThunk,
  bulkDeleteCMSPagesThunk
} from './adminDashboardThunks';

// Initial state for the admin dashboard
const initialState = {
  // Sidebar state
  activeTab: 'dashboard', // Default active tab
  isSidebarOpen: false,

  // Admin profile data (mock data for development)
  profile: {
    firstName: 'Admin',
    lastName: 'User',
    email: '<EMAIL>',
    phone: '+****************',
    profileImage: null,
    role: 'admin',
  },

  // Dashboard statistics (will be populated by API)
  stats: {
    totalBuyers: 0,
    totalSellers: 0,
    totalContent: 0,
    totalRevenue: 0,
    pendingApprovals: 0,
    activeUsers: 0,
    monthlyRevenue: 0,
    monthlyOrders: 0,
  },

  // Loading states
  loading: {
    stats: false,
    users: false,
    content: false,
    bids: false,
    reports: false,
    cms: false,
    cmsPages: false,
    settings: false,
    approval: false,
    userDetail: false,
    contentDetail: false,
    bidDetail: false,
    offers: false,
    offerDetail: false,
  },

  // Error states
  errors: {
    stats: null,
    users: null,
    content: null,
    bids: null,
    reports: null,
    cms: null,
    cmsPages: null,
    settings: null,
    approval: null,
    userDetail: null,
    contentDetail: null,
    bidDetail: null,
    offers: null,
    offerDetail: null,
  },

  // UI states
  ui: {
    selectedUsers: [],
    selectedContent: [],
    selectedBids: [],
    selectedOffers: [],
    selectedCMSPages: [],
    showApprovalModal: false,
    showUserDetailModal: false,
    showContentDetailModal: false,
    showBidDetailModal: false,
    showCMSEditorModal: false,
    currentApprovalItem: null,
    currentUserDetail: null,
    currentContentDetail: null,
    currentBidDetail: null,
    currentCMSPage: null,
    bulkActionType: null,
    currentOfferDetail: null,
    showOfferDetailModal: false,
  },

  // Financial settings
  financialSettings: {
    platformCommission: 15,
    sellerPayout: 85,
    minimumPayout: 50,
    processingFee: 2.9,
    payoutSchedule: 'weekly',
    taxRate: 0,
    autoApprove: false,
    emailVerificationRequired: true,
    phoneVerificationRequired: false,
    maxFileSize: 1024, // MB - Updated to support 1GB video files
    allowedFormats: ['mp4', 'pdf', 'zip'],
    minPrice: 5,
    maxPrice: 500,
  },

  // Users data and pagination
  users: {
    data: [],
    pagination: {
      current: 1,
      pages: 1,
      total: 0,
      limit: 10
    },
    filters: {
      search: '',
      role: '',
      status: '',
      sortBy: 'createdAt',
      sortOrder: 'desc',
      dateFrom: '',
      dateTo: ''
    }
  },

  // Content data and pagination
  content: {
    data: [],
    pagination: {
      current: 1,
      pages: 1,
      total: 0,
      limit: 10
    },
    filters: {
      search: '',
      status: '',
      contentType: '',
      sport: '',
      seller: '',
      sortBy: 'createdAt',
      sortOrder: 'desc',
      dateFrom: '',
      dateTo: ''
    }
  },

  // Bids data and pagination
  bids: {
    data: [],
    pagination: {
      current: 1,
      pages: 1,
      total: 0,
      limit: 10
    },
    filters: {
      search: '',
      status: '',
      contentId: '',
      bidderId: '',
      sortBy: 'createdAt',
      sortOrder: 'desc',
      dateFrom: '',
      dateTo: '',
      minAmount: '',
      maxAmount: ''
    }
  },

  // Bid stats
  bidStats: {
    totalBids: 0,
    activeBids: 0,
    wonBids: 0,
    lostBids: 0,
    averageBidAmount: 0,
    totalBidValue: 0
  },

  // Pending approvals (will be populated by API)
  pendingApprovals: [],

  // Recent activity (will be populated by API)
  recentActivity: [],

  // CMS Pages data
  cmsPages: [],

  // Analytics data for charts (API data)
  analytics: {
    revenue: [],
    userRegistrations: [],
    contentByCategory: [],
    revenueBySport: []
  },

  // Additional API data
  topPerformers: {
    topContent: [],
    topSellers: [],
    topBuyers: []
  },

  systemHealth: null,
  userStats: null,
  contentStats: null,
  currentUserDetail: null,
  userActivityHistory: [],
  contentReviews: [],

  // Offers data and pagination
  offers: {
    data: [],
    pagination: {
      current: 1,
      pages: 1,
      total: 0,
      limit: 10
    },
    filters: {
      search: '',
      status: '',
      contentId: '',
      buyerId: '',
      sellerId: '',
      sortBy: 'createdAt',
      sortOrder: 'desc',
      dateFrom: '',
      dateTo: '',
      minAmount: '',
      maxAmount: ''
    }
  },

  // Offer stats
  offerStats: {
    totalOffers: 0,
    pendingOffers: 0,
    acceptedOffers: 0,
    rejectedOffers: 0,
    averageOfferAmount: 0,
    totalOfferValue: 0
  }
};

// Admin dashboard slice
const adminDashboardSlice = createSlice({
  name: 'adminDashboard',
  initialState,
  reducers: {
    // Sidebar actions
    setActiveTab: (state, action) => {
      state.activeTab = action.payload;
    },
    toggleSidebar: (state) => {
      state.isSidebarOpen = !state.isSidebarOpen;
    },
    setSidebarOpen: (state, action) => {
      state.isSidebarOpen = action.payload;
    },

    // Profile actions
    updateProfile: (state, action) => {
      state.profile = { ...state.profile, ...action.payload };
    },

    // Stats actions
    updateStats: (state, action) => {
      state.stats = { ...state.stats, ...action.payload };
    },
    setStatsLoading: (state, action) => {
      state.loading.stats = action.payload;
    },
    setStatsError: (state, action) => {
      state.errors.stats = action.payload;
    },

    // Users actions
    addUser: (state, action) => {
      state.users.data.push(action.payload);
    },
    updateUserLocal: (state, action) => {
      const index = state.users.data.findIndex(user => user.id === action.payload.id);
      if (index !== -1) {
        state.users.data[index] = { ...state.users.data[index], ...action.payload };
      }
    },
    deleteUserLocal: (state, action) => {
      state.users.data = state.users.data.filter(user => user.id !== action.payload);
    },
    setUsersLoading: (state, action) => {
      state.loading.users = action.payload;
    },
    setUsersError: (state, action) => {
      state.errors.users = action.payload;
    },

    // Content actions
    addContent: (state, action) => {
      state.content.data.push(action.payload);
    },
    updateContentLocal: (state, action) => {
      const index = state.content.data.findIndex(item => item.id === action.payload.id);
      if (index !== -1) {
        state.content.data[index] = { ...state.content.data[index], ...action.payload };
      }
    },
    deleteContentLocal: (state, action) => {
      state.content.data = state.content.data.filter(item => item.id !== action.payload);
    },
    approveContentLocal: (state, action) => {
      const index = state.content.data.findIndex(item => item.id === action.payload);
      if (index !== -1) {
        state.content.data[index].status = 'published';
      }
      // Remove from pending approvals
      state.pendingApprovals = state.pendingApprovals.filter(
        item => item.id !== action.payload
      );
    },
    rejectContentLocal: (state, action) => {
      const index = state.content.data.findIndex(item => item.id === action.payload);
      if (index !== -1) {
        state.content.data[index].status = 'rejected';
      }
      // Remove from pending approvals
      state.pendingApprovals = state.pendingApprovals.filter(
        item => item.id !== action.payload
      );
    },
    setContentLoading: (state, action) => {
      state.loading.content = action.payload;
    },
    setContentError: (state, action) => {
      state.errors.content = action.payload;
    },

    // Bid actions
    addBid: (state, action) => {
      state.bids.data.push(action.payload);
    },
    setBidsLoading: (state, action) => {
      state.loading.bids = action.payload;
    },
    setBidsError: (state, action) => {
      state.errors.bids = action.payload;
    },

    // CMS Pages actions
    addCMSPage: (state, action) => {
      state.cmsPages.push(action.payload);
    },
    updateCMSPage: (state, action) => {
      const index = state.cmsPages.findIndex(page => page.id === action.payload.id);
      if (index !== -1) {
        state.cmsPages[index] = { ...state.cmsPages[index], ...action.payload };
      }
    },
    deleteCMSPageLocal: (state, action) => {
      state.cmsPages = state.cmsPages.filter(page => page.id !== action.payload);
    },
    setCMSLoading: (state, action) => {
      state.loading.cms = action.payload;
    },
    setCMSError: (state, action) => {
      state.errors.cms = action.payload;
    },

    // Activity actions
    addActivity: (state, action) => {
      state.recentActivity.unshift(action.payload);
      // Keep only the latest 10 activities
      if (state.recentActivity.length > 10) {
        state.recentActivity = state.recentActivity.slice(0, 10);
      }
    },

    // Analytics actions
    updateAnalytics: (state, action) => {
      state.analytics = { ...state.analytics, ...action.payload };
    },

    // Clear all errors
    clearErrors: (state) => {
      state.errors = {
        stats: null,
        users: null,
        content: null,
        bids: null,
        reports: null,
        cms: null,
        settings: null,
      };
    },

    // Reset dashboard state
    resetDashboard: () => {
      return initialState;
    },

    // UI Actions
    setSelectedUsers: (state, action) => {
      state.ui.selectedUsers = action.payload;
    },
    setSelectedContent: (state, action) => {
      state.ui.selectedContent = action.payload;
    },
    setSelectedBids: (state, action) => {
      state.ui.selectedBids = action.payload;
    },
    setSelectedOffers: (state, action) => {
      state.ui.selectedOffers = action.payload;
    },
    setSelectedCMSPages: (state, action) => {
      state.ui.selectedCMSPages = action.payload;
    },
    showApprovalModal: (state, action) => {
      state.ui.showApprovalModal = true;
      state.ui.currentApprovalItem = action.payload;
    },
    hideApprovalModal: (state) => {
      state.ui.showApprovalModal = false;
      state.ui.currentApprovalItem = null;
    },
    showUserDetailModal: (state, action) => {
      state.ui.showUserDetailModal = true;
      state.ui.currentUserDetail = action.payload;
    },
    hideUserDetailModal: (state) => {
      state.ui.showUserDetailModal = false;
      state.ui.currentUserDetail = null;
    },
    showContentDetailModal: (state, action) => {
      state.ui.showContentDetailModal = true;
      state.ui.currentContentDetail = action.payload;
    },
    hideContentDetailModal: (state) => {
      state.ui.showContentDetailModal = false;
      state.ui.currentContentDetail = null;
    },
    showBidDetailModal: (state, action) => {
      state.ui.showBidDetailModal = true;
      state.ui.currentBidDetail = action.payload;
    },
    hideBidDetailModal: (state) => {
      state.ui.showBidDetailModal = false;
      state.ui.currentBidDetail = null;
    },
    showCMSEditorModal: (state, action) => {
      state.ui.showCMSEditorModal = true;
      state.ui.currentCMSPage = action.payload;
    },
    hideCMSEditorModal: (state) => {
      state.ui.showCMSEditorModal = false;
      state.ui.currentCMSPage = null;
    },
    setBulkActionType: (state, action) => {
      state.ui.bulkActionType = action.payload;
    },

    // Financial Settings Actions
    updateFinancialSettings: (state, action) => {
      state.financialSettings = { ...state.financialSettings, ...action.payload };
    },

    // Bulk User Actions
    bulkUpdateUsersLocal: (state, action) => {
      const { userIds, updates } = action.payload;
      state.users.data = state.users.data.map(user =>
        userIds.includes(user.id) ? { ...user, ...updates } : user
      );
    },
    bulkDeleteUsersLocal: (state, action) => {
      state.users.data = state.users.data.filter(user => !action.payload.includes(user.id));
    },

    // Bulk Content Actions
    bulkUpdateContentLocal: (state, action) => {
      const { contentIds, updates } = action.payload;
      state.content.data = state.content.data.map(item =>
        contentIds.includes(item.id) ? { ...item, ...updates } : item
      );
    },
    bulkDeleteContentLocal: (state, action) => {
      state.content.data = state.content.data.filter(item => !action.payload.includes(item.id));
    },

    // Loading Actions
    setApprovalLoading: (state, action) => {
      state.loading.approval = action.payload;
    },
    setUserDetailLoading: (state, action) => {
      state.loading.userDetail = action.payload;
    },
    setContentDetailLoading: (state, action) => {
      state.loading.contentDetail = action.payload;
    },
    setBidDetailLoading: (state, action) => {
      state.loading.bidDetail = action.payload;
    },

    // Error Actions
    setApprovalError: (state, action) => {
      state.errors.approval = action.payload;
    },
    setUserDetailError: (state, action) => {
      state.errors.userDetail = action.payload;
    },
    setContentDetailError: (state, action) => {
      state.errors.contentDetail = action.payload;
    },
    setBidDetailError: (state, action) => {
      state.errors.bidDetail = action.payload;
    },

    // Filter actions
    setUserFilters: (state, action) => {
      state.users.filters = { ...state.users.filters, ...action.payload };
    },
    setContentFilters: (state, action) => {
      state.content.filters = { ...state.content.filters, ...action.payload };
    },
    setBidFilters: (state, action) => {
      state.bids.filters = { ...state.bids.filters, ...action.payload };
    },

    // Offer actions
    addOffer: (state, action) => {
      state.offers.data.push(action.payload);
    },
    updateOfferLocal: (state, action) => {
      const index = state.offers.data.findIndex(offer => offer._id === action.payload._id);
      if (index !== -1) {
        state.offers.data[index] = { ...state.offers.data[index], ...action.payload };
      }
    },
    deleteOfferLocal: (state, action) => {
      state.offers.data = state.offers.data.filter(offer => offer._id !== action.payload);
    },
    setOffersLoading: (state, action) => {
      state.loading.offers = action.payload;
    },
    setOffersError: (state, action) => {
      state.errors.offers = action.payload;
    },
    setOfferFilters: (state, action) => {
      state.offers.filters = { ...state.offers.filters, ...action.payload };
    },
    showOfferDetailModal: (state, action) => {
      state.ui.showOfferDetailModal = true;
      state.ui.currentOfferDetail = action.payload;
    },
    hideOfferDetailModal: (state) => {
      state.ui.showOfferDetailModal = false;
      state.ui.currentOfferDetail = null;
    },
  },
  extraReducers: (builder) => {
    // Dashboard Stats
    builder
      .addCase(fetchDashboardStats.pending, (state) => {
        state.loading.stats = true;
        state.errors.stats = null;
      })
      .addCase(fetchDashboardStats.fulfilled, (state, action) => {
        state.loading.stats = false;
        // Map API response structure to frontend state structure
        const apiData = action.payload;
        state.stats = {
          totalBuyers: apiData.users?.buyers || 0,
          totalSellers: apiData.users?.sellers || 0,
          totalContent: apiData.content?.total || 0,
          totalRevenue: apiData.revenue?.total || 0,
          pendingApprovals: apiData.content?.pending || 0,
          activeUsers: apiData.users?.active || 0,
          monthlyRevenue: apiData.revenue?.monthly || 0,
          monthlyOrders: apiData.orders?.thisMonth || 0,
          // Additional stats from API
          totalUsers: apiData.users?.total || 0,
          publishedContent: apiData.content?.published || 0,
          draftContent: apiData.content?.draft || 0,
          newUsersThisMonth: apiData.users?.newThisMonth || 0,
          newContentThisMonth: apiData.content?.newThisMonth || 0,
          totalOrders: apiData.orders?.total || 0,
          completedOrders: apiData.orders?.completed || 0,
          pendingOrders: apiData.orders?.pending || 0,
          platformFees: apiData.revenue?.platformFees || 0,
          sellerEarnings: apiData.revenue?.sellerEarnings || 0,
          monthlyPlatformFees: apiData.revenue?.monthlyPlatformFees || 0,
        };
      })
      .addCase(fetchDashboardStats.rejected, (state, action) => {
        state.loading.stats = false;
        state.errors.stats = action.payload;
      })

      // Recent Activity
      .addCase(fetchRecentActivity.pending, (state) => {
        state.loading.stats = true;
      })
      .addCase(fetchRecentActivity.fulfilled, (state, action) => {
        state.loading.stats = false;
        state.recentActivity = action.payload || [];
      })
      .addCase(fetchRecentActivity.rejected, (state, action) => {
        state.loading.stats = false;
        state.errors.stats = action.payload;
      })

      // Pending Approvals
      .addCase(fetchPendingApprovals.pending, (state) => {
        state.loading.approval = true;
      })
      .addCase(fetchPendingApprovals.fulfilled, (state, action) => {
        state.loading.approval = false;
        // Combine content and seller approvals into a flat array
        const apiData = action.payload;
        const contentApprovals = (apiData.content || []).map(item => ({
          id: item.id,
          type: 'content',
          contentTitle: item.title,
          seller: item.seller,
          category: item.category,
          sport: item.sport,
          submissionDate: item.submissionDate
        }));
        const sellerApprovals = (apiData.sellers || []).map(item => ({
          id: item.id,
          type: 'seller_verification',
          contentTitle: `Seller Verification: ${item.name}`,
          seller: item.name,
          category: 'Verification',
          submissionDate: item.submissionDate
        }));
        state.pendingApprovals = [...contentApprovals, ...sellerApprovals];
      })
      .addCase(fetchPendingApprovals.rejected, (state, action) => {
        state.loading.approval = false;
        state.errors.approval = action.payload;
      })

      // Analytics
      .addCase(fetchAnalytics.pending, (state) => {
        state.loading.stats = true;
      })
      .addCase(fetchAnalytics.fulfilled, (state, action) => {
        state.loading.stats = false;
        state.analytics = action.payload || {
          revenue: [],
          userRegistrations: [],
          contentByCategory: [],
          revenueBySport: []
        };
      })
      .addCase(fetchAnalytics.rejected, (state, action) => {
        state.loading.stats = false;
        state.errors.stats = action.payload;
      })

      // Top Performers
      .addCase(fetchTopPerformers.fulfilled, (state, action) => {
        state.topPerformers = action.payload || {
          topContent: [],
          topSellers: [],
          topBuyers: []
        };
      })

      // System Health
      .addCase(fetchSystemHealth.fulfilled, (state, action) => {
        state.systemHealth = action.payload || {};
      })

      // Users
      .addCase(fetchUsers.pending, (state) => {
        state.loading.users = true;
        state.errors.users = null;
      })
      .addCase(fetchUsers.fulfilled, (state, action) => {
        state.loading.users = false;
        state.users.data = action.payload.users || [];
        state.users.pagination = action.payload.pagination || {
          page: 1,
          limit: 10,
          total: 0,
          pages: 0
        };
      })
      .addCase(fetchUsers.rejected, (state, action) => {
        state.loading.users = false;
        state.errors.users = action.payload;
      })

      // User Detail
      .addCase(fetchUserById.pending, (state) => {
        state.loading.userDetail = true;
        state.errors.userDetail = null;
      })
      .addCase(fetchUserById.fulfilled, (state, action) => {
        state.loading.userDetail = false;
        state.currentUserDetail = action.payload || null;
      })
      .addCase(fetchUserById.rejected, (state, action) => {
        state.loading.userDetail = false;
        state.errors.userDetail = action.payload;
      })

      // Create User
      .addCase(createUser.fulfilled, (state, action) => {
        state.users.data.unshift(action.payload);
        state.users.pagination.total += 1;
      })

      // Update User
      .addCase(updateUser.fulfilled, (state, action) => {
        const index = state.users.data.findIndex(user => user._id === action.payload._id);
        if (index !== -1) {
          state.users.data[index] = action.payload;
        }
        if (state.currentUserDetail && state.currentUserDetail.user._id === action.payload._id) {
          state.currentUserDetail.user = action.payload;
        }
      })

      // Delete User
      .addCase(deleteUser.fulfilled, (state, action) => {
        state.users.data = state.users.data.filter(user => user._id !== action.payload);
        state.users.pagination.total -= 1;
      })

      // Toggle User Status
      .addCase(toggleUserStatus.fulfilled, (state, action) => {
        const index = state.users.data.findIndex(user => user._id === action.payload._id);
        if (index !== -1) {
          state.users.data[index] = action.payload;
        }
      })

      // Verify User
      .addCase(verifyUser.fulfilled, (state, action) => {
        const index = state.users.data.findIndex(user => user._id === action.payload._id);
        if (index !== -1) {
          state.users.data[index] = action.payload;
        }
      })

      // User Stats
      .addCase(fetchUserStats.fulfilled, (state, action) => {
        state.userStats = action.payload || {};
      })

      // User Activity History
      .addCase(fetchUserActivityHistory.fulfilled, (state, action) => {
        state.userActivityHistory = action.payload || [];
      })

      // Content Management
      .addCase(fetchContent.pending, (state) => {
        state.loading.content = true;
        state.errors.content = null;
      })
      .addCase(fetchContent.fulfilled, (state, action) => {
        state.loading.content = false;
        state.content.data = action.payload.content || [];
        state.content.pagination = action.payload.pagination || {
          page: 1,
          limit: 10,
          total: 0,
          pages: 0
        };
      })
      .addCase(fetchContent.rejected, (state, action) => {
        state.loading.content = false;
        state.errors.content = action.payload;
      })

      // Content Detail
      .addCase(fetchContentById.pending, (state) => {
        state.loading.contentDetail = true;
        state.errors.contentDetail = null;
      })
      .addCase(fetchContentById.fulfilled, (state, action) => {
        state.loading.contentDetail = false;
        state.ui.currentContentDetail = action.payload || null;
      })
      .addCase(fetchContentById.rejected, (state, action) => {
        state.loading.contentDetail = false;
        state.errors.contentDetail = action.payload;
      })

      // Update Content
      .addCase(updateContent.pending, (state) => {
        state.loading.contentDetail = true;
        state.errors.contentDetail = null;
      })
      .addCase(updateContent.fulfilled, (state, action) => {
        state.loading.contentDetail = false;
        // Update the content in the list
        const index = state.content.data.findIndex(item => item._id === action.payload._id);
        if (index !== -1) {
          state.content.data[index] = action.payload;
        }
        // Update the current content detail
        state.ui.currentContentDetail = action.payload;
      })
      .addCase(updateContent.rejected, (state, action) => {
        state.loading.contentDetail = false;
        state.errors.contentDetail = action.payload;
      })

      // Approve Content
      .addCase(approveContent.fulfilled, (state, action) => {
        const index = state.content.data.findIndex(item => item._id === action.payload._id);
        if (index !== -1) {
          state.content.data[index] = action.payload;
        }
        // Remove from pending approvals
        state.pendingApprovals = state.pendingApprovals.filter(
          item => !(item.type === 'content' && item.id === action.payload._id)
        );
      })

      // Reject Content
      .addCase(rejectContent.fulfilled, (state, action) => {
        const index = state.content.data.findIndex(item => item._id === action.payload._id);
        if (index !== -1) {
          state.content.data[index] = action.payload;
        }
        // Remove from pending approvals
        state.pendingApprovals = state.pendingApprovals.filter(
          item => !(item.type === 'content' && item.id === action.payload._id)
        );
      })

      // Delete Content
      .addCase(deleteContent.fulfilled, (state, action) => {
        state.content.data = state.content.data.filter(item => item._id !== action.payload);
        state.content.pagination.total -= 1;
      })

      // Update Content Status
      .addCase(updateContentStatus.fulfilled, (state, action) => {
        const index = state.content.data.findIndex(item => item._id === action.payload._id);
        if (index !== -1) {
          state.content.data[index] = action.payload;
        }
      })

      // Feature Content
      .addCase(featureContent.fulfilled, (state, action) => {
        const index = state.content.data.findIndex(item => item._id === action.payload._id);
        if (index !== -1) {
          state.content.data[index] = action.payload;
        }
      })

      // Unfeature Content
      .addCase(unfeatureContent.fulfilled, (state, action) => {
        const index = state.content.data.findIndex(item => item._id === action.payload._id);
        if (index !== -1) {
          state.content.data[index] = action.payload;
        }
      })

      // Content Stats
      .addCase(fetchContentStats.fulfilled, (state, action) => {
        state.contentStats = action.payload || {};
      })

      // Content Reviews
      .addCase(fetchContentReviews.fulfilled, (state, action) => {
        state.contentReviews = action.payload || [];
      })

      // Bids
      .addCase(fetchBids.pending, (state) => {
        state.loading.bids = true;
        state.errors.bids = null;
      })
      .addCase(fetchBids.fulfilled, (state, action) => {
        state.loading.bids = false;
        state.bids.data = action.payload.bids || [];
        state.bids.pagination = action.payload.pagination || {
          current: 1,
          pages: 1,
          total: 0,
          limit: 10
        };
      })
      .addCase(fetchBids.rejected, (state, action) => {
        state.loading.bids = false;
        state.errors.bids = action.payload;
      })

      // Bid Detail
      .addCase(fetchBidById.pending, (state) => {
        state.loading.bidDetail = true;
        state.errors.bidDetail = null;
      })
      .addCase(fetchBidById.fulfilled, (state, action) => {
        state.loading.bidDetail = false;
        state.ui.currentBidDetail = action.payload || null;
      })
      .addCase(fetchBidById.rejected, (state, action) => {
        state.loading.bidDetail = false;
        state.errors.bidDetail = action.payload;
      })

      // Approve Bid
      .addCase(approveBid.fulfilled, (state, action) => {
        const index = state.bids.data.findIndex(bid => bid._id === action.payload.id);
        if (index !== -1) {
          state.bids.data[index] = action.payload.data;
        }
      })

      // Reject Bid
      .addCase(rejectBid.fulfilled, (state, action) => {
        const index = state.bids.data.findIndex(bid => bid._id === action.payload.id);
        if (index !== -1) {
          state.bids.data[index] = action.payload.data;
        }
      })

      // Delete Bid
      .addCase(deleteBid.fulfilled, (state, action) => {
        state.bids.data = state.bids.data.filter(bid => bid._id !== action.payload);
        state.bids.pagination.total -= 1;
      })

      // Update Bid
      .addCase(updateBid.fulfilled, (state, action) => {
        const index = state.bids.data.findIndex(bid => bid._id === action.payload.id);
        if (index !== -1) {
          state.bids.data[index] = action.payload.data;
        }
      })

      // Bid Stats
      .addCase(getBidStats.fulfilled, (state, action) => {
        state.bidStats = action.payload || {
          totalBids: 0,
          activeBids: 0,
          wonBids: 0,
          lostBids: 0,
          averageBidAmount: 0,
          totalBidValue: 0
        };
      })

      // Bulk Bid Actions
      .addCase(bulkApproveBids.fulfilled, (state, action) => {
        action.payload.bidIds.forEach(bidId => {
          const index = state.bids.data.findIndex(bid => bid._id === bidId);
          if (index !== -1) {
            state.bids.data[index].status = 'Active';
          }
        });
      })

      .addCase(bulkRejectBids.fulfilled, (state, action) => {
        action.payload.bidIds.forEach(bidId => {
          const index = state.bids.data.findIndex(bid => bid._id === bidId);
          if (index !== -1) {
            state.bids.data[index].status = 'Rejected';
          }
        });
      })

      .addCase(bulkDeleteBids.fulfilled, (state, action) => {
        state.bids.data = state.bids.data.filter(
          bid => !action.payload.bidIds.includes(bid._id)
        );
        state.bids.pagination.total -= action.payload.bidIds.length;
      })

      // Bulk Actions
      .addCase(bulkUpdateUsers.fulfilled, () => {
        // Refresh users list after bulk update
        // This could be optimized to update specific users
      })

      .addCase(bulkDeleteUsers.fulfilled, (state, action) => {
        // Remove deleted users from the list
        state.users.data = state.users.data.filter(
          user => !action.payload.userIds.includes(user._id)
        );
        state.users.pagination.total -= action.payload.result.data.total;
      })

      .addCase(bulkApproveContent.fulfilled, (state, action) => {
        // Update content status for approved items
        action.payload.contentIds.forEach(contentId => {
          const index = state.content.data.findIndex(item => item._id === contentId);
          if (index !== -1) {
            state.content.data[index].status = 'Published';
          }
        });
        // Remove from pending approvals
        state.pendingApprovals = state.pendingApprovals.filter(
          item => !(item.type === 'content' && action.payload.contentIds.includes(item.id))
        );
      })

      .addCase(bulkRejectContent.fulfilled, (state, action) => {
        // Update content status for rejected items
        action.payload.contentIds.forEach(contentId => {
          const index = state.content.data.findIndex(item => item._id === contentId);
          if (index !== -1) {
            state.content.data[index].status = 'Rejected';
          }
        });
        // Remove from pending approvals
        state.pendingApprovals = state.pendingApprovals.filter(
          item => !(item.type === 'content' && action.payload.contentIds.includes(item.id))
        );
      })

      .addCase(bulkDeleteContent.fulfilled, (state, action) => {
        // Remove deleted content from the list
        state.content.data = state.content.data.filter(
          item => !action.payload.contentIds.includes(item._id)
        );
        state.content.pagination.total -= action.payload.result.data.total;
      });

    // Offers
    builder
      .addCase(fetchOffers.pending, (state) => {
        state.loading.offers = true;
        state.errors.offers = null;
      })
      .addCase(fetchOffers.fulfilled, (state, action) => {
        state.loading.offers = false;
        state.offers.data = action.payload || [];
        const apiPagination = action.payload.pagination || {};
        state.offers.pagination = {
          current: apiPagination.page || 1,
          pages: apiPagination.pages || 1,
          total: apiPagination.total || 0,
          limit: apiPagination.limit || 10
        };
      })
      .addCase(fetchOffers.rejected, (state, action) => {
        state.loading.offers = false;
        state.errors.offers = action.payload;
      })

      // Offer Detail
      .addCase(fetchOfferById.pending, (state) => {
        state.loading.offerDetail = true;
        state.errors.offerDetail = null;
      })
      .addCase(fetchOfferById.fulfilled, (state, action) => {
        state.loading.offerDetail = false;
        state.ui.currentOfferDetail = action.payload || null;
      })
      .addCase(fetchOfferById.rejected, (state, action) => {
        state.loading.offerDetail = false;
        state.errors.offerDetail = action.payload;
      })

      // Approve Offer
      .addCase(approveOffer.fulfilled, (state, action) => {
        const index = state.offers.data.findIndex(offer => offer._id === action.payload.id);
        if (index !== -1) {
          state.offers.data[index] = action.payload.data;
        }
      })

      // Reject Offer
      .addCase(rejectOffer.fulfilled, (state, action) => {
        const index = state.offers.data.findIndex(offer => offer._id === action.payload.id);
        if (index !== -1) {
          state.offers.data[index] = action.payload.data;
        }
      })

      // Delete Offer
      .addCase(deleteOffer.fulfilled, (state, action) => {
        state.offers.data = state.offers.data.filter(offer => offer._id !== action.payload);
        state.offers.pagination.total -= 1;
      })

      // Bulk Approve Offers
      .addCase(bulkApproveOffers.fulfilled, (state, action) => {
        action.payload.offerIds.forEach(offerId => {
          const index = state.offers.data.findIndex(offer => offer._id === offerId);
          if (index !== -1) {
            state.offers.data[index].status = 'Accepted';
          }
        });
      })

      // Bulk Reject Offers
      .addCase(bulkRejectOffers.fulfilled, (state, action) => {
        action.payload.offerIds.forEach(offerId => {
          const index = state.offers.data.findIndex(offer => offer._id === offerId);
          if (index !== -1) {
            state.offers.data[index].status = 'Rejected';
          }
        });
      })

      // Bulk Delete Offers
      .addCase(bulkDeleteOffers.fulfilled, (state, action) => {
        state.offers.data = state.offers.data.filter(
          offer => !action.payload.offerIds.includes(offer._id)
        );
        state.offers.pagination.total -= action.payload.offerIds.length;
      })

      // Offer Stats
      .addCase(getOfferStats.fulfilled, (state, action) => {
        state.offerStats = action.payload || {
          totalOffers: 0,
          pendingOffers: 0,
          acceptedOffers: 0,
          rejectedOffers: 0,
          averageOfferAmount: 0,
          totalOfferValue: 0
        };
      })

      // CMS Pages
      .addCase(fetchCMSPages.pending, (state) => {
        state.loading.cmsPages = true;
        state.errors.cmsPages = null;
      })
      .addCase(fetchCMSPages.fulfilled, (state, action) => {
        state.loading.cmsPages = false;
        state.cmsPages = action.payload || [];
      })
      .addCase(fetchCMSPages.rejected, (state, action) => {
        state.loading.cmsPages = false;
        state.errors.cmsPages = action.payload;
      })

      // Delete CMS Page
      .addCase(deleteCMSPage.fulfilled, (state, action) => {
        state.cmsPages = state.cmsPages.filter(page => page._id !== action.payload);
      })

      // Update CMS Page Status
      .addCase(updateCMSPageStatusThunk.fulfilled, (state, action) => {
        const index = state.cmsPages.findIndex(page => page._id === action.payload.id);
        if (index !== -1) {
          state.cmsPages[index].status = action.payload.status;
        }
      })

      // Bulk Delete CMS Pages
      .addCase(bulkDeleteCMSPagesThunk.fulfilled, (state, action) => {
        state.cmsPages = state.cmsPages.filter(
          page => !action.payload.pageIds.includes(page._id)
        );
      });
  },
});

// Export actions
export const {
  setActiveTab,
  toggleSidebar,
  setSidebarOpen,
  updateProfile,
  updateStats,
  setStatsLoading,
  setStatsError,
  addUser,
  updateUserLocal,
  deleteUserLocal,
  setUsersLoading,
  setUsersError,
  addContent,
  updateContentLocal,
  deleteContentLocal,
  approveContentLocal,
  rejectContentLocal,
  setContentLoading,
  setContentError,
  addBid,
  setBidsLoading,
  setBidsError,
  addCMSPage,
  updateCMSPage,
  deleteCMSPageLocal,
  setCMSLoading,
  setCMSError,
  addActivity,
  updateAnalytics,
  clearErrors,
  resetDashboard,
  // UI Actions
  setSelectedUsers,
  setSelectedContent,
  setSelectedBids,
  setSelectedOffers,
  setSelectedCMSPages,
  showApprovalModal,
  hideApprovalModal,
  showUserDetailModal,
  hideUserDetailModal,
  showContentDetailModal,
  hideContentDetailModal,
  showBidDetailModal,
  hideBidDetailModal,
  showCMSEditorModal,
  hideCMSEditorModal,
  setBulkActionType,
  // Financial Settings
  updateFinancialSettings,
  // Bulk Actions
  bulkUpdateUsersLocal,
  bulkDeleteUsersLocal,
  bulkUpdateContentLocal,
  bulkDeleteContentLocal,
  // Loading Actions
  setApprovalLoading,
  setUserDetailLoading,
  setContentDetailLoading,
  setBidDetailLoading,
  // Error Actions
  setApprovalError,
  setUserDetailError,
  setContentDetailError,
  setBidDetailError,
  // Filter actions
  setUserFilters,
  setContentFilters,
  setBidFilters,
  // Offer actions
  addOffer,
  updateOfferLocal,
  deleteOfferLocal,
  setOffersLoading,
  setOffersError,
  setOfferFilters,
  showOfferDetailModal,
  hideOfferDetailModal,
} = adminDashboardSlice.actions;

// Selectors
export const selectActiveTab = (state) => state.adminDashboard.activeTab;
export const selectIsSidebarOpen = (state) => state.adminDashboard.isSidebarOpen;
export const selectProfile = (state) => state.adminDashboard.profile;
export const selectStats = (state) => state.adminDashboard.stats;
export const selectUsers = (state) => state.adminDashboard.users;
export const selectContent = (state) => state.adminDashboard.content;
export const selectBids = (state) => state.adminDashboard.bids;
export const selectPendingApprovals = (state) => state.adminDashboard.pendingApprovals;
export const selectRecentActivity = (state) => state.adminDashboard.recentActivity;
export const selectCMSPages = (state) => state.adminDashboard.cmsPages;
export const selectAnalytics = (state) => state.adminDashboard.analytics;

// Selector with fallback for analytics data
export const selectAnalyticsWithFallback = (state) => {
  const analytics = state.adminDashboard.analytics;
  return analytics || {
    salesChart: { labels: [], data: [] },
    userRegistrations: { labels: [], data: [] },
    categoryDistribution: { labels: [], data: [] },
    revenueByCategory: { labels: [], data: [] }
  };
};

export const selectLoading = (state) => state.adminDashboard.loading;
export const selectErrors = (state) => state.adminDashboard.errors;
export const selectUI = (state) => state.adminDashboard.ui;
export const selectFinancialSettings = (state) => state.adminDashboard.financialSettings;
export const selectSelectedUsers = (state) => state.adminDashboard.ui.selectedUsers;
export const selectSelectedContent = (state) => state.adminDashboard.ui.selectedContent;
export const selectSelectedBids = (state) => state.adminDashboard.ui.selectedBids;
export const selectSelectedOffers = (state) => state.adminDashboard.ui.selectedOffers;
export const selectSelectedCMSPages = (state) => state.adminDashboard.ui.selectedCMSPages;
export const selectCurrentApprovalItem = (state) => state.adminDashboard.ui.currentApprovalItem;
export const selectCurrentUserDetail = (state) => state.adminDashboard.ui.currentUserDetail;
export const selectCurrentContentDetail = (state) => state.adminDashboard.ui.currentContentDetail;
export const selectCurrentBidDetail = (state) => state.adminDashboard.ui.currentBidDetail;
export const selectCurrentCMSPage = (state) => state.adminDashboard.ui.currentCMSPage;

// New selectors for API data
export const selectTopPerformers = (state) => state.adminDashboard.topPerformers;
export const selectSystemHealth = (state) => state.adminDashboard.systemHealth;
export const selectUserStats = (state) => state.adminDashboard.userStats;
export const selectContentStats = (state) => state.adminDashboard.contentStats;
export const selectUserActivityHistory = (state) => state.adminDashboard.userActivityHistory;
export const selectContentReviews = (state) => state.adminDashboard.contentReviews;
export const selectUserFilters = (state) => state.adminDashboard.users.filters;
export const selectContentFilters = (state) => state.adminDashboard.content.filters;
export const selectBidFilters = (state) => state.adminDashboard.bids.filters;
export const selectUsersPagination = (state) => state.adminDashboard.users.pagination;
export const selectContentPagination = (state) => state.adminDashboard.content.pagination;
export const selectBidsPagination = (state) => state.adminDashboard.bids.pagination;
export const selectBidStats = (state) => state.adminDashboard.bidStats;
export const selectOffers = (state) => state.adminDashboard.offers;
export const selectOfferStats = (state) => state.adminDashboard.offerStats;
export const selectOfferFilters = (state) => state.adminDashboard.offers.filters;
export const selectOffersPagination = (state) => state.adminDashboard.offers.pagination;
export const selectCurrentOfferDetail = (state) => state.adminDashboard.ui.currentOfferDetail;

export default adminDashboardSlice.reducer;
