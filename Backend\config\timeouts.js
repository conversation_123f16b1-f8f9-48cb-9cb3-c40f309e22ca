// Payment and auction timeout configurations
// Set to 10 minutes for testing purposes

const timeouts = {
    // Payment deadlines - 10 minutes for testing
    PAYMENT_DEADLINE_HOURS: 0.17, // 10 minutes
    PAYMENT_DEADLINE_MS: 10 * 60 * 1000, // 10 minutes in milliseconds

    // Background job intervals - faster for testing
    ORDER_CLEANUP_INTERVAL: '*/2 * * * *', // every 2 minutes
    FRAUD_DETECTION_INTERVAL: '*/5 * * * *', // every 5 minutes
    RUNNER_UP_NOTIFICATION_INTERVAL: '*/1 * * * *', // every 1 minute

    // File upload timeouts
    UPLOAD_TIMEOUT_MS: 3600 * 1000, // 1 hour for large video uploads
    UPLOAD_TIMEOUT_SECONDS: 3600, // 1 hour in seconds

    // Other timeouts
    S3_URL_EXPIRATION: 3600, // 1 hour
    SESSION_TIMEOUT: 5 * 60 * 1000, // 5 minutes

    // Auction extensions
    AUCTION_EXTENSION_HOURS: 0.5, // 30 minutes
    AUCTION_EXTENSION_MS: 30 * 60 * 1000, // 30 minutes in milliseconds

    // Auction conversion interval
    AUCTION_CONVERSION_INTERVAL: '*/5 * * * *', // Every 5 minutes - check for expired auctions
};

// Helper functions
const getPaymentDeadline = (fromDate = new Date()) => {
    return new Date(fromDate.getTime() + timeouts.PAYMENT_DEADLINE_MS);
};

const getOrderExpiryTime = (fromDate = new Date()) => {
    return new Date(fromDate.getTime() + timeouts.PAYMENT_DEADLINE_MS);
};

const getAuctionExtension = (fromDate = new Date()) => {
    return new Date(fromDate.getTime() + timeouts.AUCTION_EXTENSION_MS);
};

const formatTimeRemaining = (deadline) => {
    const now = new Date();
    const remaining = deadline - now;

    if (remaining <= 0) return 'Expired';

    const minutes = Math.floor(remaining / (1000 * 60));
    const hours = Math.floor(minutes / 60);

    if (hours > 0) return `${hours}h ${minutes % 60}m`;
    return `${minutes}m`;
};

module.exports = {
    ...timeouts,
    getPaymentDeadline,
    getOrderExpiryTime,
    getAuctionExtension,
    formatTimeRemaining,
}; 